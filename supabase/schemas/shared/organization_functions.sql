-- Organization Management Functions
-- This file contains functions for organization operations
CREATE OR REPLACE FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text" DEFAULT NULL,
	"logo_url" "text" DEFAULT NULL
) RETURNS "json" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	new_org public.organization;
	user_id uuid := auth.uid();
BEGIN
	-- Ensure user is authenticated
	IF user_id IS NULL THEN
		RAISE EXCEPTION 'Not authenticated';
	END IF;

	-- Create organization
	INSERT INTO public.organization(name, description, logo_url, created_by_user_id)
	VALUES (name, description, logo_url, user_id)
	RETURNING * INTO new_org;

	-- Return JSON object
	RETURN json_build_object(
		'org_id', new_org.org_id,
		'name', new_org.name,
		'description', new_org.description,
		'logo_url', new_org.logo_url
	);
END;
$$;

ALTER FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) IS 'Creates a new organization and returns JSON with organization details';

CREATE OR REPLACE FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") RETURNS TABLE (
	"org_id" "uuid",
	"name" "text",
	"description" "text",
	"logo_url" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN QUERY
	SELECT o.org_id, o.name, o.description, o.logo_url, o.created_by_user_id, o.created_at, o.updated_at
	FROM public.organization o
	WHERE o.name = org_name_param
	AND public.current_user_has_entity_access('organization'::public.entity_type, o.org_id);
END;
$$;

ALTER FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") IS 'Gets organization details by name for users with access';

-- Dashboard data function to get recent projects and clients with permissions
CREATE OR REPLACE FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") RETURNS "json" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	projects_data json;
	clients_data json;
	user_id uuid := auth.uid();
BEGIN
	-- Ensure user is authenticated
	IF user_id IS NULL THEN
		RAISE EXCEPTION 'Not authenticated';
	END IF;

	-- Verify user has access to the organization
	IF NOT public.current_user_has_entity_access('organization'::public.entity_type, org_id_param) THEN
		RAISE EXCEPTION 'Access denied to organization';
	END IF;

	-- Get recent projects with client and organization info
	SELECT json_agg(
		json_build_object(
			'project_id', p.project_id,
			'name', p.name,
			'description', p.description,
			'created_at', p.created_at,
			'updated_at', p.updated_at,
			'client', json_build_object(
				'name', c.name,
				'organization', json_build_object(
					'name', o.name
				)
			),
			'is_project_admin', public.current_user_has_entity_role('project', p.project_id, 'admin'),
			'is_client_admin', public.current_user_has_entity_role('client', c.client_id, 'admin'),
			'is_org_admin', public.current_user_has_entity_role('organization', o.org_id, 'admin')
		)
	) INTO projects_data
	FROM public.project p
	INNER JOIN public.client c ON c.client_id = p.client_id
	INNER JOIN public.organization o ON o.org_id = c.org_id
	WHERE o.org_id = org_id_param
	AND public.current_user_has_entity_access('project'::public.entity_type, p.project_id)
	ORDER BY p.updated_at DESC
	LIMIT 3;

	-- Get recent clients with project count and permissions
	SELECT json_agg(
		json_build_object(
			'client_id', c.client_id,
			'name', c.name,
			'description', c.description,
			'logo_url', c.logo_url,
			'client_url', c.client_url,
			'internal_url', c.internal_url,
			'internal_url_description', c.internal_url_description,
			'org_id', c.org_id,
			'created_at', c.created_at,
			'updated_at', c.updated_at,
			'created_by_user_id', c.created_by_user_id,
			'organization', json_build_object(
				'name', o.name
			),
			'project_count', (
				SELECT COUNT(*)
				FROM public.project p2
				WHERE p2.client_id = c.client_id
				AND public.current_user_has_entity_access('project'::public.entity_type, p2.project_id)
			),
			'is_client_admin', public.current_user_has_entity_role('client', c.client_id, 'admin'),
			'is_org_admin', public.current_user_has_entity_role('organization', c.org_id, 'admin')
		)
	) INTO clients_data
	FROM public.client c
	INNER JOIN public.organization o ON o.org_id = c.org_id
	WHERE c.org_id = org_id_param
	AND public.current_user_has_entity_access('client'::public.entity_type, c.client_id)
	ORDER BY c.updated_at DESC
	LIMIT 3;

	-- Return combined data
	RETURN json_build_object(
		'projects', COALESCE(projects_data, '[]'::json),
		'clients', COALESCE(clients_data, '[]'::json)
	);
END;
$$;

ALTER FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") OWNER TO "postgres";

GRANT
EXECUTE ON FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") TO authenticated;

GRANT
EXECUTE ON FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") TO service_role;

COMMENT ON FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") IS 'Gets dashboard data including recent projects and clients with permissions for an organization';
