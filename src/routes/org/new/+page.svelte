<script lang="ts">
	import type { PageProps } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import SpinnerGapIcon from 'phosphor-svelte/lib/SpinnerGap';

	const { data }: PageProps = $props();
	const formHandler = superForm(data.form);
	const { form, enhance, delayed, submitting } = formHandler;
</script>

<div class="container mx-auto max-w-md py-16">
	<h1 class="mb-6 text-2xl font-semibold">Create Organization</h1>
	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-4">
				<Form.Field form={formHandler} name="name">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Name <span class="text-red-500">*</span></Form.Label>
							<Input
								{...props}
								type="text"
								placeholder="Organization Name"
								bind:value={$form.name}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={formHandler} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description</Form.Label>
							<Textarea
								{...props}
								placeholder="Describe your organization"
								bind:value={$form.description}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={formHandler} name="logo_url">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Logo URL</Form.Label>
							<Input
								{...props}
								type="url"
								placeholder="https://example.com/logo.png"
								bind:value={$form.logo_url}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="pt-6">
					<Form.Button class="w-full" disabled={$submitting}>
						{#if $delayed}
							<SpinnerGapIcon class="text-primary size-4 animate-spin" />
						{/if}
						Create Organization
					</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
