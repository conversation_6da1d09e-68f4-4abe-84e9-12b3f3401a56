<script lang="ts">
	import { dev } from '$app/environment';
	import Button from '$lib/components/ui/button/button.svelte';
	import { getCurrentOrgId } from '$lib/current-org.svelte.js';
	import { formatDate } from '$lib/utils';

	const { data } = $props();

	const orgContext = getCurrentOrgId();
	const currentOrg = orgContext.getCurrentOrg();

	console.log({ projects: data.projects });
</script>

<div class="flex flex-col gap-8">
	<h1 class="mb-0">Welcome to Cost Atlas</h1>

	<section>
		<h2 class="my-4 text-2xl">Your Recent Projects</h2>
		{#if data.projects.length === 0}
			<div class="rounded-lg border border-dashed p-8 text-center">
				<p class="text-muted-foreground">Choose a client to get your first project started.</p>
			</div>
		{:else}
			<div class="rounded-lg border">
				<table class="w-full table-fixed">
					<thead>
						<tr class="bg-muted/50 border-b">
							<th class="px-4 py-3 text-left font-medium">Name</th>
							<th class="px-4 py-3 text-left font-medium">Description</th>
							<th class="px-4 py-3 text-left font-medium">Client</th>
							<th class="w-32 px-4 py-3 text-left font-medium">Created</th>
							<th class="w-28 px-4 py-3 text-right font-medium">Actions</th>
						</tr>
					</thead>
					<tbody>
						{#each data.projects as project (project.project_id)}
							<tr class="hover:bg-muted/50 border-b last:border-b-0">
								<td class="px-4 py-3 font-medium">
									<a
										href="/org/{encodeURIComponent(
											project.client.organization.name,
										)}/clients/{encodeURIComponent(
											project.client.name,
										)}/projects/{encodeURIComponent(project.name)}"
										class="hover:text-input hover:underline"
									>
										{project.name}
									</a>
								</td>
								<td class="text-muted-foreground px-4 py-3">
									{project.description || '-'}
								</td>
								<td class="text-muted-foreground px-4 py-3">
									<a
										class="hover:text-input rounded-md hover:underline"
										href="/org/{encodeURIComponent(
											project.client.organization.name,
										)}/clients/{encodeURIComponent(project.client.name)}"
										>{project.client.name || '-'}</a
									>
								</td>
								<td class="text-muted-foreground px-4 py-3">
									{formatDate(project.created_at)}
								</td>
								<td class="px-4 py-3 text-right">
									<Button
										href="/org/{encodeURIComponent(
											project.client.organization.name,
										)}/clients/{encodeURIComponent(
											project.client.name,
										)}/projects/{encodeURIComponent(project.name)}/edit"
										variant="outline"
										size="sm"
									>
										Edit
									</Button>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		{/if}
	</section>

	<section>
		<h2 class="my-4 text-2xl">Your Recent Clients</h2>
		{#if data.clients.length === 0}
			<div class="rounded-lg border border-dashed p-8 text-center">
				<p class="text-muted-foreground">
					No clients found. Create your first client to get started.
				</p>
			</div>
		{:else}
			<div class="rounded-lg border">
				<table class="w-full table-fixed">
					<thead>
						<tr class="bg-muted/50 border-b">
							<th class="px-4 py-3 text-left font-medium">Name</th>
							<th class="px-4 py-3 text-left font-medium">Projects</th>
							<th class="px-4 py-3 text-left font-medium">Links</th>
							<th class="w-32 px-4 py-3 text-left font-medium">Created</th>
							<th class="w-28 px-4 py-3 text-right font-medium">Actions</th>
						</tr>
					</thead>
					<tbody>
						{#each data.clients as client (client.client_id)}
							<tr class="hover:bg-muted/50 border-b last:border-b-0">
								<td class="px-4 py-3">
									<a
										href="/org/{encodeURIComponent(
											client.organization.name,
										)}/clients/{encodeURIComponent(client.name)}"
										class="hover:text-input font-medium hover:underline"
									>
										{client.name}
									</a>
									{#if client.description}
										<div class="text-muted-foreground mt-1 text-sm">
											{client.description}
										</div>
									{/if}
								</td>
								<td class="text-muted-foreground px-4 py-3">
									{client.projectCount}
								</td>

								<td class="px-4 py-3">
									<div class="flex flex-wrap gap-2">
										{#if client.client_url}
											<a
												href={client.client_url}
												target="_blank"
												rel="noopener noreferrer"
												class="text-sm text-blue-600 hover:underline"
											>
												Website
											</a>
										{/if}
										{#if client.internal_url}
											<a
												href={client.internal_url}
												target="_blank"
												rel="noopener noreferrer"
												class="text-sm text-blue-600 hover:underline"
											>
												{client.internal_url_description || 'Internal'}
											</a>
										{/if}
									</div>
								</td>
								<td class="text-muted-foreground px-4 py-3">
									{formatDate(client.created_at)}
								</td>
								<td class="px-4 py-3 text-right">
									{#if client.is_org_admin || client.is_client_admin}
										<Button
											href="/org/{encodeURIComponent(
												client.organization.name,
											)}/clients/{encodeURIComponent(client.name)}/edit"
											variant="outline"
											size="sm">Edit</Button
										>
									{/if}
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
			{#if currentOrg?.name}
				<div class="mt-4">
					<a
						class="text-blue-500 hover:underline"
						href="/org/{encodeURIComponent(currentOrg.name)}/clients"
					>
						View All Clients &rarr;
					</a>
				</div>
			{/if}
		{/if}
	</section>

	{#if dev}
		<section class="my-6">
			<Button href="/admin-tools">Admin Tools</Button>
		</section>
	{/if}
</div>
