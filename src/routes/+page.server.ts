import { requireUser } from '$lib/server/auth';
import type { PageServerLoad } from './$types';

// Type for the dashboard data returned by the RPC function
type DashboardData = {
	projects: Array<{
		project_id: string;
		name: string;
		description: string | null;
		created_at: string;
		updated_at: string;
		client: {
			name: string;
			organization: {
				name: string;
			};
		};
		is_project_admin: boolean;
		is_client_admin: boolean;
		is_org_admin: boolean;
	}>;
	clients: Array<{
		client_id: string;
		name: string;
		description: string | null;
		logo_url: string | null;
		client_url: string | null;
		internal_url: string | null;
		internal_url_description: string | null;
		org_id: string;
		created_at: string;
		updated_at: string;
		created_by_user_id: string;
		organization: {
			name: string;
		};
		project_count: number;
		projectCount: number; // For compatibility with frontend
		is_client_admin: boolean;
		is_org_admin: boolean;
	}>;
};

export const load = (async ({ locals }) => {
	const { orgId, supabase } = locals;

	console.log({ orgId });

	if (!orgId) {
		return { projects: [], clients: [] };
	}

	// Use the new RPC function to get dashboard data efficiently
	const { data: dashboardData, error } = await supabase.rpc('get_dashboard_data', {
		org_id_param: orgId,
	});

	if (error) {
		console.error('Error fetching dashboard data:', error);
		return { projects: [], clients: [] };
	}

	console.log({ dashboardData });

	// Cast the JSON response to our typed structure
	const typedData = dashboardData as DashboardData;

	// Map clients to include projectCount for frontend compatibility
	const clientsWithProjectCount = (typedData?.clients ?? []).map((client) => ({
		...client,
		projectCount: client.project_count,
	}));

	return {
		projects: typedData?.projects ?? [],
		clients: clientsWithProjectCount,
	};
}) satisfies PageServerLoad;
